# ORC MongoDB服务 - 重构后使用说明

## 概述

ORC MongoDB服务已成功重构为基于nohup的独立子服务架构，实现了您期望的合理逻辑：

- ✅ 使用nohup启动各个子程序，确保进程独立性
- ✅ 各子服务自行管理自己的日志文件
- ✅ 主服务专注于监控各子程序状态和结果展示
- ✅ 实现了完善的进程生命周期管理

## 架构说明

### 主服务 (main.py)
- **角色**: 进程管理和监控
- **功能**: 
  - 启动和管理子服务进程
  - 监控子服务状态
  - 提供统一的状态展示
  - 自动重启失败的子服务

### 子服务

#### 1. MongoDB写入服务 (mongodb_writer_service)
- **功能**: 从Redis队列接收数据并写入MongoDB
- **端口**: 8002
- **日志**: `logs/orc_mongodb_service/mongodb_writer_service/mongodb_writer_service_{timestamp}.log`
- **健康检查**: HTTP接口 `/health`

#### 2. ORC处理服务 (orc_processor_service)
- **功能**: 读取ORC文件，处理数据并发送到Redis队列
- **日志**: `logs/orc_mongodb_service/orc_processor_service/orc_processor_service_{timestamp}.log`
- **运行模式**: 批处理，处理完成后自动退出

## 使用方法

### 1. 启动服务（默认监控模式）
```bash
python3 services/orc_mongodb_service/run.py
```

### 2. 仅启动子服务
```bash
python3 services/orc_mongodb_service/run.py --start-services
```

### 3. 查看服务状态
```bash
python3 services/orc_mongodb_service/run.py --status
```

### 4. 指定配置文件
```bash
python3 services/orc_mongodb_service/run.py --config configs/orc_mongodb_service/production.yaml
```

## 日志管理

### 日志目录结构
```
logs/orc_mongodb_service/
├── mongodb_writer_service/
│   └── mongodb_writer_service_{timestamp}.log
├── orc_processor_service/
│   └── orc_processor_service_{timestamp}.log
└── orc_mongodb_service_{timestamp}.log  # 主服务日志
```

### 日志特点
- **独立性**: 各子服务拥有独立的日志文件
- **时间戳**: 每次启动都会创建带时间戳的新日志文件
- **自动管理**: 日志目录自动创建
- **彩色输出**: 支持彩色日志便于阅读

## 进程管理

### 自动重启配置
```yaml
process_management:
  auto_restart:
    enabled: true      # 启用自动重启
    max_restarts: 3    # 最大重启次数
    restart_interval: 30  # 重启间隔（秒）
```

### 监控配置
```yaml
process_management:
  monitoring:
    check_interval: 10        # 状态检查间隔（秒）
    health_check_timeout: 5   # 健康检查超时（秒）
```

## 监控功能

### 状态展示
主服务提供丰富的状态信息：
- 子进程状态（运行/停止/失败）
- 进程PID和运行时间
- 内存和CPU使用情况
- 日志文件位置
- 重启次数统计

### 健康检查
- MongoDB写入服务：HTTP健康检查
- ORC处理服务：进程状态检查
- 队列状态监控

## 测试验证

运行测试脚本验证重构结果：
```bash
python3 services/orc_mongodb_service/test_refactored_service.py
```

测试内容：
- ✅ 服务启动测试
- ✅ 日志独立性测试
- ✅ 进程管理测试
- ✅ 监控功能测试

## 优势

### 1. 进程独立性
- 使用nohup启动，主服务停止不影响子服务
- 各子服务可以独立重启和管理
- 进程隔离，故障不会相互影响

### 2. 日志管理
- 各服务独立管理日志文件
- 日志文件带时间戳，便于追踪
- 支持日志轮转和大小控制

### 3. 监控和管理
- 实时状态监控
- 自动故障恢复
- 详细的进程信息展示
- 手动服务管理接口

### 4. 配置灵活性
- 统一的配置文件管理
- 支持开发/生产环境配置
- 可配置的重启和监控策略

## 注意事项

1. **首次启动**: 确保配置文件正确，特别是MongoDB和Redis连接信息
2. **日志空间**: 定期清理旧的日志文件，避免磁盘空间不足
3. **进程监控**: 主服务停止后，子服务会继续运行，需要手动停止
4. **配置更新**: 修改配置后需要重启相应的服务才能生效

## 故障排除

### 1. 子服务启动失败
- 检查配置文件是否正确
- 查看子服务的日志文件
- 确认依赖服务（MongoDB、Redis）是否可用

### 2. 自动重启不工作
- 检查进程管理配置
- 确认是否达到最大重启次数
- 查看主服务日志了解重启失败原因

### 3. 监控显示异常
- 检查网络连接
- 确认服务端口是否被占用
- 查看防火墙设置

这个重构实现了您期望的合理架构，提供了更好的服务管理和监控能力。
