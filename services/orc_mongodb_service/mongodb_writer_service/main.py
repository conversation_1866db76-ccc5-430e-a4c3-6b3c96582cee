#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB写入微服务主入口

启动MongoDB写入微服务
"""

import os
import sys
import asyncio
import argparse
import signal
import time
from typing import Optional
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import ConfigManager, Logger
from services.orc_mongodb_service.mongodb_writer_service.service import MongoDBWriterService


class ServiceRunner:
    """服务运行器"""
    
    def __init__(self):
        self.service: Optional[MongoDBWriterService] = None
        self.shutdown_requested = False
        self.logger = None
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，准备关闭服务...")
            self.shutdown_requested = True
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def run(self, args):
        """运行服务"""
        try:
            # 初始化配置管理器
            config_manager = ConfigManager()

            # 设置独立的日志配置
            self._setup_independent_logging(config_manager)

            self.logger = Logger.get_logger(__name__)

            # 设置信号处理器
            self.setup_signal_handlers()

            self.logger.info("=== MongoDB写入微服务启动 ===")
            self.logger.info(f"进程PID: {os.getpid()}")
            self.logger.info(f"日志文件: {self._get_log_file_path(config_manager)}")

            # 创建服务实例
            self.service = MongoDBWriterService(config_manager)
            await self.service.initialize()

            # 运行服务
            self.logger.info(f"服务启动在 {args.host}:{args.port}")

            # 创建服务器任务
            import uvicorn
            config = uvicorn.Config(
                self.service.app,
                host=args.host,
                port=args.port,
                log_level="info"
            )
            server = uvicorn.Server(config)

            # 启动服务器
            await server.serve()

        except Exception as e:
            if self.logger:
                self.logger.error(f"服务运行失败: {e}")
            else:
                print(f"服务运行失败: {e}", file=sys.stderr)
            sys.exit(1)

        finally:
            if self.service:
                await self.service.shutdown()

    def _setup_independent_logging(self, config_manager):
        """设置独立的日志配置"""
        try:
            # 获取日志配置
            log_config = config_manager.get_config("logging", default={})

            # 为MongoDB写入服务设置独立的日志文件
            timestamp = int(time.time())
            log_file = f"logs/orc_mongodb_service/mongodb_writer_service/mongodb_writer_service_{timestamp}.log"

            # 确保日志目录存在
            log_dir = Path(log_file).parent
            log_dir.mkdir(parents=True, exist_ok=True)

            # 设置环境变量，让Logger使用独立的日志文件
            os.environ['USER_DF_LOG_FILE'] = log_file

        except Exception as e:
            print(f"设置独立日志配置失败: {e}", file=sys.stderr)

    def _get_log_file_path(self, config_manager):
        """获取日志文件路径"""
        return os.environ.get('USER_DF_LOG_FILE', 'logs/mongodb_writer_service.log')


def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="MongoDB写入微服务",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 启动服务（使用默认配置）
  python3 services/mongodb_writer_service/main.py

  # 指定主机和端口
  python3 services/mongodb_writer_service/main.py --host 0.0.0.0 --port 8002

  # 指定配置文件
  python3 services/mongodb_writer_service/main.py --config configs/orc_mongodb_service/development.yaml
        """
    )

    # 服务配置参数
    parser.add_argument("--host", default="0.0.0.0", help="服务主机地址")
    parser.add_argument("--port", type=int, default=8002, help="服务端口")

    # 配置文件参数
    parser.add_argument("--config",
                       default="configs/orc_mongodb_service/development.yaml",
                       help="配置文件路径")

    # 日志参数
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="日志级别")

    return parser


def validate_arguments(args):
    """验证命令行参数"""
    if args.port < 1 or args.port > 65535:
        raise ValueError("端口号必须在1-65535之间")


async def main():
    """主函数"""
    try:
        # 解析命令行参数
        parser = create_argument_parser()
        args = parser.parse_args()
        
        # 验证参数
        validate_arguments(args)
        
        # 设置环境变量
        if args.config:
            os.environ['USER_DF_CONFIG_FILE'] = args.config
        if args.log_level:
            os.environ['USER_DF_LOG_LEVEL'] = args.log_level
        
        # 创建并运行服务
        runner = ServiceRunner()
        await runner.run(args)
        
    except Exception as e:
        print(f"启动失败: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
